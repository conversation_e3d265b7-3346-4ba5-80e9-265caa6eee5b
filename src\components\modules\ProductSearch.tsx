import React, { useState, useEffect } from 'react';
import {
  TextInput,
  Card,
  Stack,
  Group,
  Text,
  Badge,
  Table,
  ScrollArea,
  ActionIcon,
  Tooltip,
  Avatar,
  Progress,
  Alert,
} from '@mantine/core';
import {
  IconSearch,
  IconPackage,
  IconBuilding,
  IconAlertTriangle,
  IconCheck,
  IconEye,
} from '@tabler/icons-react';
import { useDebouncedValue } from '@mantine/hooks';

interface Product {
  id: string;
  name: string;
  nameAr: string;
  sku: string;
  brand: string;
  model: string;
  oemNumbers: string[];
  inventory: {
    branchId: string;
    branchName: string;
    quantity: number;
    location: string;
  }[];
  sellingPrice: number;
  costPrice: number;
  image?: string;
}

export function ProductSearch() {
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery] = useDebouncedValue(searchQuery, 300);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);

  // Mock data for demonstration
  const mockProducts: Product[] = [
    {
      id: '1',
      name: 'Mobil 1 Engine Oil 5W-30',
      nameAr: 'زيت محرك موبيل 1 - 5W30',
      sku: 'MOB-5W30-4L',
      brand: 'Mobil',
      model: '5W-30',
      oemNumbers: ['15208-65F0C', '15208-65F0A', 'C-224J'],
      sellingPrice: 185.50,
      costPrice: 142.00,
      inventory: [
        { branchId: '1', branchName: 'الفرع الرئيسي', quantity: 25, location: 'A-1-5' },
        { branchId: '2', branchName: 'فرع جدة', quantity: 12, location: 'B-2-3' },
        { branchId: '3', branchName: 'فرع الدمام', quantity: 8, location: 'C-1-2' },
      ],
    },
    {
      id: '2',
      name: 'Toyota Camry Air Filter',
      nameAr: 'فلتر هواء تويوتا كامري',
      sku: 'TOY-AF-CAM-2018',
      brand: 'Toyota',
      model: 'Camry 2018-2023',
      oemNumbers: ['17801-0P051', '17801-0P050'],
      sellingPrice: 85.00,
      costPrice: 62.00,
      inventory: [
        { branchId: '1', branchName: 'الفرع الرئيسي', quantity: 15, location: 'D-3-1' },
        { branchId: '2', branchName: 'فرع جدة', quantity: 6, location: 'E-1-4' },
        { branchId: '3', branchName: 'فرع الدمام', quantity: 3, location: 'F-2-2' },
      ],
    },
    {
      id: '3',
      name: 'AC Delco Battery 70AH',
      nameAr: 'بطارية AC ديلكو 70 أمبير',
      sku: 'ACD-BAT-70AH',
      brand: 'AC Delco',
      model: '70AH',
      oemNumbers: ['94R-70', 'BCI-94R'],
      sellingPrice: 420.00,
      costPrice: 315.00,
      inventory: [
        { branchId: '1', branchName: 'الفرع الرئيسي', quantity: 8, location: 'G-1-1' },
        { branchId: '2', branchName: 'فرع جدة', quantity: 4, location: 'H-2-1' },
        { branchId: '3', branchName: 'فرع الدمام', quantity: 2, location: 'I-1-3' },
      ],
    },
  ];

  useEffect(() => {
    if (debouncedQuery.length >= 2) {
      setLoading(true);
      
      // Simulate API call with delay
      setTimeout(() => {
        const filtered = mockProducts.filter(product => 
          product.name.toLowerCase().includes(debouncedQuery.toLowerCase()) ||
          product.nameAr.includes(debouncedQuery) ||
          product.sku.toLowerCase().includes(debouncedQuery.toLowerCase()) ||
          product.brand.toLowerCase().includes(debouncedQuery.toLowerCase()) ||
          product.oemNumbers.some(oem => oem.toLowerCase().includes(debouncedQuery.toLowerCase()))
        );
        setProducts(filtered);
        setLoading(false);
      }, 500);
    } else {
      setProducts([]);
    }
  }, [debouncedQuery]);

  const getTotalStock = (inventory: Product['inventory']) => {
    return inventory.reduce((total, item) => total + item.quantity, 0);
  };

  const getStockStatus = (quantity: number) => {
    if (quantity === 0) return { color: 'red', label: 'نفد المخزون' };
    if (quantity <= 5) return { color: 'orange', label: 'مخزون منخفض' };
    if (quantity <= 10) return { color: 'yellow', label: 'مخزون متوسط' };
    return { color: 'green', label: 'متوفر' };
  };

  return (
    <div style={{ height: '100%', overflow: 'auto' }}>
      <Stack gap={{ base: 'md', md: 'lg' }} style={{ minHeight: '100%' }}>
        <Card withBorder p={{ base: 'md', md: 'lg' }}>
          <Stack gap="md">
            <Group justify="space-between" wrap="nowrap">
              <Text fw={600} size={{ base: 'md', md: 'lg' }}>البحث عن المنتجات</Text>
              <Badge color="blue" variant="light" size={{ base: 'sm', md: 'md' }}>
                البحث عبر جميع الفروع
              </Badge>
            </Group>
          
          <TextInput
            placeholder="ابحث بالاسم، رقم القطعة، رقم OEM، أو الماركة..."
            leftSection={<IconSearch size="1rem" />}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.currentTarget.value)}
            size={{ base: 'sm', md: 'md' }}
          />

          {debouncedQuery.length > 0 && debouncedQuery.length < 2 && (
            <Alert color="blue" variant="light">
              يرجى إدخال حرفين على الأقل للبحث
            </Alert>
          )}
        </Stack>
      </Card>

      {products.length > 0 && (
        <Card withBorder p="lg">
          <Group justify="space-between" mb="md">
            <Text fw={600} size="lg">نتائج البحث</Text>
            <Badge color="green" variant="light">
              {products.length} منتج
            </Badge>
          </Group>

          <ScrollArea>
            <Table>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>المنتج</Table.Th>
                  <Table.Th>أرقام OEM</Table.Th>
                  <Table.Th>المخزون الإجمالي</Table.Th>
                  <Table.Th>توزيع المخزون</Table.Th>
                  <Table.Th>السعر</Table.Th>
                  <Table.Th>الإجراءات</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {products.map((product) => {
                  const totalStock = getTotalStock(product.inventory);
                  const stockStatus = getStockStatus(totalStock);
                  
                  return (
                    <Table.Tr key={product.id}>
                      <Table.Td>
                        <Group gap="sm">
                          <Avatar size="md" radius="md" color="blue">
                            <IconPackage size="1.2rem" />
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{product.nameAr}</Text>
                            <Text c="dimmed" size="xs">{product.name}</Text>
                            <Text c="dimmed" size="xs">
                              {product.brand} - {product.sku}
                            </Text>
                          </div>
                        </Group>
                      </Table.Td>
                      
                      <Table.Td>
                        <Stack gap="xs">
                          {product.oemNumbers.map((oem, index) => (
                            <Badge key={index} size="xs" variant="outline">
                              {oem}
                            </Badge>
                          ))}
                        </Stack>
                      </Table.Td>
                      
                      <Table.Td>
                        <Group gap="xs">
                          <Text fw={500}>{totalStock}</Text>
                          <Badge color={stockStatus.color} size="xs">
                            {stockStatus.label}
                          </Badge>
                        </Group>
                      </Table.Td>
                      
                      <Table.Td>
                        <Stack gap="xs">
                          {product.inventory.map((inv) => (
                            <Group key={inv.branchId} gap="xs" justify="space-between">
                              <Group gap="xs">
                                <IconBuilding size="0.8rem" />
                                <Text size="xs">{inv.branchName}</Text>
                              </Group>
                              <Group gap="xs">
                                <Text size="xs" fw={500}>{inv.quantity}</Text>
                                <Text size="xs" c="dimmed">({inv.location})</Text>
                              </Group>
                            </Group>
                          ))}
                        </Stack>
                      </Table.Td>
                      
                      <Table.Td>
                        <Stack gap="xs">
                          <Text fw={500} size="sm">{product.sellingPrice.toFixed(2)} ر.س</Text>
                          <Text c="dimmed" size="xs">
                            التكلفة: {product.costPrice.toFixed(2)} ر.س
                          </Text>
                        </Stack>
                      </Table.Td>
                      
                      <Table.Td>
                        <Group gap="xs">
                          <Tooltip label="عرض التفاصيل">
                            <ActionIcon variant="light" color="blue" size="sm">
                              <IconEye size="1rem" />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  );
                })}
              </Table.Tbody>
            </Table>
          </ScrollArea>
        </Card>
      )}

        {loading && (
          <Card withBorder p={{ base: 'md', md: 'lg' }}>
            <Group gap="md">
              <Text size={{ base: 'sm', md: 'md' }}>جاري البحث...</Text>
              <Progress value={100} animated style={{ flex: 1 }} />
            </Group>
          </Card>
        )}
      </Stack>
    </div>
  );
}
