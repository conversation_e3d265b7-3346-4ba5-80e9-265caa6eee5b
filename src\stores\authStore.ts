import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type UserType = 'OWNER' | 'EMPLOYEE';

export interface CompanyOwner {
  id: string;
  companyId: string;
  email: string;
  fullName: string;
  fullNameAr?: string;
  phone?: string;
  avatar?: string;
  isEmailVerified: boolean;
  sessionTimeout: number;
}

export interface Employee {
  id: string;
  companyId: string;
  branchId: string;
  employeeNumber: string;
  fullName: string;
  fullNameAr?: string;
  email?: string;
  phone?: string;
  role: string;
  permissions: string[];
  avatar?: string;
  salary?: number;
  hireDate?: Date;
  isActive: boolean;
  requiresApproval: boolean;
  sessionTimeout: number;
}

export interface AuthUser {
  type: UserType;
  owner?: CompanyOwner;
  employee?: Employee;
}

export interface Company {
  id: string;
  name: string;
  nameAr: string;
  logo?: string;
  settings: Record<string, any>;
}

export interface Branch {
  id: string;
  name: string;
  nameAr: string;
  companyId: string;
  address: string;
  phone: string;
  isMain: boolean;
}

export interface LoginCredentials {
  companyCode: string;
  userType: UserType;
  // For owners
  email?: string;
  // For employees
  employeeNumber?: string;
  password: string;
  rememberMe?: boolean;
}

export interface AuthSession {
  token: string;
  refreshToken: string;
  expiresAt: Date;
  userType: UserType;
  userId: string;
}

interface AuthState {
  isAuthenticated: boolean;
  user: AuthUser | null;
  company: Company | null;
  branch: Branch | null;
  session: AuthSession | null;
  availableBranches: Branch[];
  loginMode: UserType;

  // Actions
  setLoginMode: (mode: UserType) => void;
  validateCompany: (companyCode: string) => Promise<Company | null>;
  loginOwner: (credentials: LoginCredentials) => Promise<boolean>;
  loginEmployee: (credentials: LoginCredentials) => Promise<boolean>;
  logout: () => void;
  refreshSession: () => Promise<boolean>;
  switchBranch: (branchId: string) => Promise<boolean>;
  initialize: () => Promise<void>;
  updateUser: (userData: Partial<CompanyOwner | Employee>) => void;
  checkPermission: (permission: string) => boolean;
  requestPasswordReset: (email: string) => Promise<boolean>;
  resetPassword: (token: string, newPassword: string) => Promise<boolean>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      user: null,
      company: null,
      branch: null,
      session: null,
      availableBranches: [],
      loginMode: 'OWNER',

      setLoginMode: (mode) => {
        set({ loginMode: mode });
      },

      validateCompany: async (companyCode) => {
        try {
          // TODO: Replace with actual API call
          // Simulate company validation
          if (companyCode === 'DIM001') {
            const mockCompany: Company = {
              id: '1',
              name: 'Dimensions Auto Parts',
              nameAr: 'أبعاد لقطع غيار السيارات',
              settings: {},
            };
            set({ company: mockCompany });
            return mockCompany;
          }
          return null;
        } catch (error) {
          console.error('Company validation failed:', error);
          return null;
        }
      },

      loginOwner: async (credentials) => {
        try {
          // Debug logging
          console.log('Owner login attempt:', {
            email: credentials.email,
            password: credentials.password,
            expectedEmail: '<EMAIL>',
            expectedPassword: 'Owner123!@#$'
          });

          // TODO: Replace with actual API call
          if (credentials.email === '<EMAIL>' && credentials.password === 'Owner123!@#$') {
            const mockOwner: CompanyOwner = {
              id: 'owner-1',
              companyId: '1',
              email: credentials.email,
              fullName: 'أحمد محمد المالك',
              fullNameAr: 'أحمد محمد المالك',
              phone: '+966501234567',
              isEmailVerified: true,
              sessionTimeout: 7200, // 2 hours
            };

            const mockSession: AuthSession = {
              token: 'owner-jwt-token',
              refreshToken: 'owner-refresh-token',
              expiresAt: new Date(Date.now() + 7200 * 1000),
              userType: 'OWNER',
              userId: mockOwner.id,
            };

            const mockBranches: Branch[] = [
              {
                id: '1',
                name: 'Main Branch',
                nameAr: 'الفرع الرئيسي',
                companyId: '1',
                address: 'الرياض، المملكة العربية السعودية',
                phone: '+966501234567',
                isMain: true,
              },
              {
                id: '2',
                name: 'Jeddah Branch',
                nameAr: 'فرع جدة',
                companyId: '1',
                address: 'جدة، المملكة العربية السعودية',
                phone: '+966502345678',
                isMain: false,
              },
            ];

            set({
              isAuthenticated: true,
              user: { type: 'OWNER', owner: mockOwner },
              session: mockSession,
              availableBranches: mockBranches,
              branch: mockBranches[0], // Default to main branch
            });

            return true;
          }
          return false;
        } catch (error) {
          console.error('Owner login failed:', error);
          return false;
        }
      },

      loginEmployee: async (credentials) => {
        try {
          // TODO: Replace with actual API call
          if (credentials.employeeNumber === 'EMP001' && credentials.password === 'Emp123!@') {
            const mockEmployee: Employee = {
              id: 'emp-1',
              companyId: '1',
              branchId: '1',
              employeeNumber: credentials.employeeNumber,
              fullName: 'سارة أحمد الموظفة',
              fullNameAr: 'سارة أحمد الموظفة',
              email: '<EMAIL>',
              phone: '+966502345678',
              role: 'MANAGER',
              permissions: ['products.read', 'inventory.read', 'sales.create', 'customers.read'],
              isActive: true,
              requiresApproval: false,
              sessionTimeout: 28800, // 8 hours
            };

            const mockSession: AuthSession = {
              token: 'employee-jwt-token',
              refreshToken: 'employee-refresh-token',
              expiresAt: new Date(Date.now() + 28800 * 1000),
              userType: 'EMPLOYEE',
              userId: mockEmployee.id,
            };

            const mockBranch: Branch = {
              id: mockEmployee.branchId,
              name: 'Main Branch',
              nameAr: 'الفرع الرئيسي',
              companyId: '1',
              address: 'الرياض، المملكة العربية السعودية',
              phone: '+966501234567',
              isMain: true,
            };

            set({
              isAuthenticated: true,
              user: { type: 'EMPLOYEE', employee: mockEmployee },
              session: mockSession,
              branch: mockBranch,
              availableBranches: [mockBranch], // Employees see only their branch
            });

            return true;
          }
          return false;
        } catch (error) {
          console.error('Employee login failed:', error);
          return false;
        }
      },

      logout: () => {
        // TODO: Invalidate session on server
        set({
          isAuthenticated: false,
          user: null,
          company: null,
          branch: null,
          session: null,
          availableBranches: [],
        });
      },

      refreshSession: async () => {
        try {
          const { session } = get();
          if (!session) return false;

          // TODO: Replace with actual API call
          // Simulate session refresh
          const newSession: AuthSession = {
            ...session,
            token: 'new-' + session.token,
            expiresAt: new Date(Date.now() + (session.userType === 'OWNER' ? 7200 : 28800) * 1000),
          };

          set({ session: newSession });
          return true;
        } catch (error) {
          console.error('Session refresh failed:', error);
          return false;
        }
      },

      switchBranch: async (branchId) => {
        try {
          const { availableBranches, user } = get();

          // Only owners can switch branches
          if (user?.type !== 'OWNER') {
            return false;
          }

          const branch = availableBranches.find(b => b.id === branchId);
          if (branch) {
            set({ branch });
            return true;
          }
          return false;
        } catch (error) {
          console.error('Branch switch failed:', error);
          return false;
        }
      },

      initialize: async () => {
        try {
          console.log('Initializing authentication system...');

          // Check for existing session and validate
          const { session } = get();
          if (session && new Date() < session.expiresAt) {
            // Session is still valid, attempt refresh
            await get().refreshSession();
          } else if (session) {
            // Session expired, logout
            get().logout();
          }
        } catch (error) {
          console.error('Initialization failed:', error);
        }
      },

      updateUser: (userData) => {
        const { user } = get();
        if (!user) return;

        if (user.type === 'OWNER' && user.owner) {
          set({
            user: {
              ...user,
              owner: { ...user.owner, ...userData },
            },
          });
        } else if (user.type === 'EMPLOYEE' && user.employee) {
          set({
            user: {
              ...user,
              employee: { ...user.employee, ...userData },
            },
          });
        }
      },

      checkPermission: (permission) => {
        const { user } = get();
        if (!user) return false;

        // Owners have all permissions
        if (user.type === 'OWNER') return true;

        // Check employee permissions
        if (user.type === 'EMPLOYEE' && user.employee) {
          return user.employee.permissions.includes(permission) ||
                 user.employee.permissions.includes('*');
        }

        return false;
      },

      requestPasswordReset: async (email) => {
        try {
          // TODO: Implement actual password reset request
          console.log('Password reset requested for:', email);
          return true;
        } catch (error) {
          console.error('Password reset request failed:', error);
          return false;
        }
      },

      resetPassword: async (token, newPassword) => {
        try {
          // TODO: Implement actual password reset
          console.log('Password reset with token:', token);
          return true;
        } catch (error) {
          console.error('Password reset failed:', error);
          return false;
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        company: state.company,
        branch: state.branch,
        session: state.session,
        loginMode: state.loginMode,
      }),
    }
  )
);
