// Permission system for role-based access control
import React from 'react';

export enum Permission {
  // Dashboard
  DASHBOARD_VIEW = 'dashboard.view',
  
  // Products
  PRODUCTS_VIEW = 'products.view',
  PRODUCTS_CREATE = 'products.create',
  PRODUCTS_EDIT = 'products.edit',
  PRODUCTS_DELETE = 'products.delete',
  PRODUCTS_IMPORT = 'products.import',
  PRODUCTS_EXPORT = 'products.export',
  
  // Inventory
  INVENTORY_VIEW = 'inventory.view',
  INVENTORY_ADJUST = 'inventory.adjust',
  INVENTORY_TRANSFER = 'inventory.transfer',
  INVENTORY_COUNT = 'inventory.count',
  
  // Sales
  SALES_VIEW = 'sales.view',
  SALES_CREATE = 'sales.create',
  SALES_EDIT = 'sales.edit',
  SALES_DELETE = 'sales.delete',
  SALES_DISCOUNT = 'sales.discount',
  SALES_REFUND = 'sales.refund',
  
  // Credit Sales
  CREDIT_SALES_VIEW = 'credit_sales.view',
  CREDIT_SALES_CREATE = 'credit_sales.create',
  CREDIT_SALES_APPROVE = 'credit_sales.approve',
  CREDIT_SALES_COLLECT = 'credit_sales.collect',
  
  // Purchases
  PURCHASES_VIEW = 'purchases.view',
  PURCHASES_CREATE = 'purchases.create',
  PURCHASES_EDIT = 'purchases.edit',
  PURCHASES_DELETE = 'purchases.delete',
  PURCHASES_APPROVE = 'purchases.approve',
  
  // Customers
  CUSTOMERS_VIEW = 'customers.view',
  CUSTOMERS_CREATE = 'customers.create',
  CUSTOMERS_EDIT = 'customers.edit',
  CUSTOMERS_DELETE = 'customers.delete',
  
  // Suppliers
  SUPPLIERS_VIEW = 'suppliers.view',
  SUPPLIERS_CREATE = 'suppliers.create',
  SUPPLIERS_EDIT = 'suppliers.edit',
  SUPPLIERS_DELETE = 'suppliers.delete',
  
  // Employees
  EMPLOYEES_VIEW = 'employees.view',
  EMPLOYEES_CREATE = 'employees.create',
  EMPLOYEES_EDIT = 'employees.edit',
  EMPLOYEES_DELETE = 'employees.delete',
  EMPLOYEES_APPROVE = 'employees.approve',
  
  // Commissions
  COMMISSIONS_VIEW = 'commissions.view',
  COMMISSIONS_CALCULATE = 'commissions.calculate',
  COMMISSIONS_PAY = 'commissions.pay',
  
  // Marketing
  MARKETING_VIEW = 'marketing.view',
  MARKETING_CREATE = 'marketing.create',
  MARKETING_SEND = 'marketing.send',
  
  // Accounting
  ACCOUNTING_VIEW = 'accounting.view',
  ACCOUNTING_CREATE = 'accounting.create',
  ACCOUNTING_EDIT = 'accounting.edit',
  ACCOUNTING_CLOSE_PERIOD = 'accounting.close_period',
  
  // ZATCA
  ZATCA_VIEW = 'zatca.view',
  ZATCA_GENERATE = 'zatca.generate',
  ZATCA_SUBMIT = 'zatca.submit',
  
  // B2B
  B2B_VIEW = 'b2b.view',
  B2B_TRADE = 'b2b.trade',
  
  // Reports
  REPORTS_VIEW = 'reports.view',
  REPORTS_EXPORT = 'reports.export',
  REPORTS_FINANCIAL = 'reports.financial',
  
  // Settings
  SETTINGS_VIEW = 'settings.view',
  SETTINGS_EDIT = 'settings.edit',
  SETTINGS_COMPANY = 'settings.company',
  SETTINGS_USERS = 'settings.users',
  SETTINGS_SYSTEM = 'settings.system',
  
  // Admin
  ADMIN_ALL = '*',
}

// Role definitions with their default permissions
export const ROLE_PERMISSIONS: Record<string, Permission[]> = {
  // Owner has all permissions
  OWNER: [Permission.ADMIN_ALL],
  
  // Manager has most permissions except system settings
  MANAGER: [
    Permission.DASHBOARD_VIEW,
    Permission.PRODUCTS_VIEW, Permission.PRODUCTS_CREATE, Permission.PRODUCTS_EDIT,
    Permission.INVENTORY_VIEW, Permission.INVENTORY_ADJUST, Permission.INVENTORY_TRANSFER,
    Permission.SALES_VIEW, Permission.SALES_CREATE, Permission.SALES_EDIT, Permission.SALES_DISCOUNT,
    Permission.CREDIT_SALES_VIEW, Permission.CREDIT_SALES_CREATE, Permission.CREDIT_SALES_APPROVE,
    Permission.PURCHASES_VIEW, Permission.PURCHASES_CREATE, Permission.PURCHASES_EDIT,
    Permission.CUSTOMERS_VIEW, Permission.CUSTOMERS_CREATE, Permission.CUSTOMERS_EDIT,
    Permission.SUPPLIERS_VIEW, Permission.SUPPLIERS_CREATE, Permission.SUPPLIERS_EDIT,
    Permission.EMPLOYEES_VIEW, Permission.EMPLOYEES_CREATE, Permission.EMPLOYEES_EDIT,
    Permission.COMMISSIONS_VIEW, Permission.COMMISSIONS_CALCULATE,
    Permission.MARKETING_VIEW, Permission.MARKETING_CREATE,
    Permission.ACCOUNTING_VIEW, Permission.ACCOUNTING_CREATE,
    Permission.ZATCA_VIEW, Permission.ZATCA_GENERATE,
    Permission.REPORTS_VIEW, Permission.REPORTS_EXPORT,
    Permission.SETTINGS_VIEW, Permission.SETTINGS_EDIT,
  ],
  
  // Cashier focused on sales operations
  CASHIER: [
    Permission.DASHBOARD_VIEW,
    Permission.PRODUCTS_VIEW,
    Permission.INVENTORY_VIEW,
    Permission.SALES_VIEW, Permission.SALES_CREATE, Permission.SALES_REFUND,
    Permission.CUSTOMERS_VIEW, Permission.CUSTOMERS_CREATE,
    Permission.ZATCA_VIEW, Permission.ZATCA_GENERATE,
  ],
  
  // Salesperson focused on sales and customers
  SALESPERSON: [
    Permission.DASHBOARD_VIEW,
    Permission.PRODUCTS_VIEW,
    Permission.INVENTORY_VIEW,
    Permission.SALES_VIEW, Permission.SALES_CREATE, Permission.SALES_DISCOUNT,
    Permission.CREDIT_SALES_VIEW, Permission.CREDIT_SALES_CREATE,
    Permission.CUSTOMERS_VIEW, Permission.CUSTOMERS_CREATE, Permission.CUSTOMERS_EDIT,
    Permission.COMMISSIONS_VIEW,
    Permission.MARKETING_VIEW,
  ],
  
  // Accountant focused on financial operations
  ACCOUNTANT: [
    Permission.DASHBOARD_VIEW,
    Permission.SALES_VIEW,
    Permission.CREDIT_SALES_VIEW, Permission.CREDIT_SALES_COLLECT,
    Permission.PURCHASES_VIEW,
    Permission.CUSTOMERS_VIEW,
    Permission.SUPPLIERS_VIEW,
    Permission.COMMISSIONS_VIEW, Permission.COMMISSIONS_PAY,
    Permission.ACCOUNTING_VIEW, Permission.ACCOUNTING_CREATE, Permission.ACCOUNTING_EDIT,
    Permission.ZATCA_VIEW, Permission.ZATCA_GENERATE, Permission.ZATCA_SUBMIT,
    Permission.REPORTS_VIEW, Permission.REPORTS_EXPORT, Permission.REPORTS_FINANCIAL,
  ],
  
  // Warehouse staff focused on inventory
  WAREHOUSE: [
    Permission.DASHBOARD_VIEW,
    Permission.PRODUCTS_VIEW,
    Permission.INVENTORY_VIEW, Permission.INVENTORY_ADJUST, Permission.INVENTORY_TRANSFER, Permission.INVENTORY_COUNT,
    Permission.PURCHASES_VIEW,
  ],
};

// Helper functions for permission checking
export class PermissionManager {
  static hasPermission(userPermissions: string[], requiredPermission: Permission): boolean {
    // Admin has all permissions
    if (userPermissions.includes(Permission.ADMIN_ALL)) {
      return true;
    }
    
    // Check if user has the specific permission
    return userPermissions.includes(requiredPermission);
  }
  
  static hasAnyPermission(userPermissions: string[], requiredPermissions: Permission[]): boolean {
    return requiredPermissions.some(permission => 
      this.hasPermission(userPermissions, permission)
    );
  }
  
  static hasAllPermissions(userPermissions: string[], requiredPermissions: Permission[]): boolean {
    return requiredPermissions.every(permission => 
      this.hasPermission(userPermissions, permission)
    );
  }
  
  static getPermissionsForRole(role: string): Permission[] {
    return ROLE_PERMISSIONS[role] || [];
  }
  
  static canAccessModule(userPermissions: string[], module: string): boolean {
    const modulePermissions: Record<string, Permission[]> = {
      dashboard: [Permission.DASHBOARD_VIEW],
      products: [Permission.PRODUCTS_VIEW],
      inventory: [Permission.INVENTORY_VIEW],
      pos: [Permission.SALES_CREATE],
      sales: [Permission.SALES_VIEW],
      'credit-sales': [Permission.CREDIT_SALES_VIEW],
      purchases: [Permission.PURCHASES_VIEW],
      customers: [Permission.CUSTOMERS_VIEW],
      suppliers: [Permission.SUPPLIERS_VIEW],
      employees: [Permission.EMPLOYEES_VIEW],
      commissions: [Permission.COMMISSIONS_VIEW],
      marketing: [Permission.MARKETING_VIEW],
      accounting: [Permission.ACCOUNTING_VIEW],
      zatca: [Permission.ZATCA_VIEW],
      b2b: [Permission.B2B_VIEW],
      reports: [Permission.REPORTS_VIEW],
      settings: [Permission.SETTINGS_VIEW],
    };
    
    const requiredPermissions = modulePermissions[module];
    if (!requiredPermissions) return false;
    
    return this.hasAnyPermission(userPermissions, requiredPermissions);
  }
}

// Permission-based component wrapper
export function withPermission<T extends Record<string, any>>(
  Component: React.ComponentType<T>,
  requiredPermission: Permission,
  fallback?: React.ComponentType
) {
  return function PermissionWrapper(props: T) {
    // This would be implemented with the auth store
    // const { user } = useAuthStore();
    // const hasPermission = user ? PermissionManager.hasPermission(user.permissions, requiredPermission) : false;

    // For now, return the component (implement actual permission check later)
    return React.createElement(Component, props);
  };
}
