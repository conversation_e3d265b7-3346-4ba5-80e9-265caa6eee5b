import React, { useState, useEffect } from 'react';
import {
  Grid,
  Card,
  Text,
  Group,
  Stack,
  Progress,
  Badge,
  ActionIcon,
  Title,
  SimpleGrid,
  RingProgress,
  Center,
  ThemeIcon,
  Paper,
  Table,
  ScrollArea,
} from '@mantine/core';
import {
  IconTrendingUp,
  IconTrendingDown,
  IconCash,
  IconShoppingCart,
  IconPackage,
  IconUsers,
  IconRefresh,
  IconAlertTriangle,
  IconCheck,
} from '@tabler/icons-react';
import { LineChart, AreaChart, BarChart } from '@mantine/charts';

interface KPICardProps {
  title: string;
  value: string;
  change: number;
  icon: React.ComponentType<any>;
  color: string;
}

function KPICard({ title, value, change, icon: Icon, color }: KPICardProps) {
  const isPositive = change >= 0;
  
  return (
    <Card withBorder p="lg" radius="md">
      <Group justify="space-between">
        <div>
          <Text c="dimmed" size="sm" tt="uppercase" fw={700}>
            {title}
          </Text>
          <Text fw={700} size="xl">
            {value}
          </Text>
          <Group gap="xs" mt="xs">
            {isPositive ? (
              <IconTrendingUp size="1rem" color="green" />
            ) : (
              <IconTrendingDown size="1rem" color="red" />
            )}
            <Text c={isPositive ? 'green' : 'red'} size="sm" fw={500}>
              {Math.abs(change)}%
            </Text>
          </Group>
        </div>
        <ThemeIcon color={color} variant="light" size="xl" radius="md">
          <Icon size="1.5rem" />
        </ThemeIcon>
      </Group>
    </Card>
  );
}

export function Dashboard() {
  const [refreshing, setRefreshing] = useState(false);
  const [salesData] = useState([
    { month: 'يناير', sales: 120000, profit: 24000 },
    { month: 'فبراير', sales: 135000, profit: 27000 },
    { month: 'مارس', sales: 148000, profit: 29600 },
    { month: 'أبريل', sales: 162000, profit: 32400 },
    { month: 'مايو', sales: 178000, profit: 35600 },
    { month: 'يونيو', sales: 195000, profit: 39000 },
  ]);

  const [inventoryAlerts] = useState([
    { product: 'زيت موبيل 1 - 5W30', currentStock: 5, minStock: 10, status: 'low' },
    { product: 'إطار ميشلان 205/55R16', currentStock: 2, minStock: 5, status: 'critical' },
    { product: 'بطارية AC Delco 70AH', currentStock: 8, minStock: 10, status: 'low' },
    { product: 'فلتر هواء تويوتا كامري', currentStock: 15, minStock: 10, status: 'ok' },
  ]);

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  return (
    <div style={{ height: '100%', overflow: 'auto' }}>
      <Stack gap={{ base: 'md', md: 'lg' }} style={{ minHeight: '100%' }}>
        <Group justify="space-between" wrap="nowrap">
          <Title order={{ base: 3, md: 2 }} size={{ base: 'h3', md: 'h2' }}>لوحة التحكم</Title>
          <ActionIcon
            variant="light"
            color="blue"
            size={{ base: 'md', md: 'lg' }}
            loading={refreshing}
            onClick={handleRefresh}
          >
            <IconRefresh size={{ base: '1rem', md: '1.2rem' }} />
          </ActionIcon>
        </Group>

        {/* KPI Cards */}
        <SimpleGrid
          cols={{ base: 1, xs: 2, sm: 2, md: 4 }}
          spacing={{ base: 'sm', md: 'lg' }}
        >
        <KPICard
          title="إجمالي المبيعات اليوم"
          value="45,250 ر.س"
          change={12.5}
          icon={IconCash}
          color="green"
        />
        <KPICard
          title="عدد الفواتير"
          value="127"
          change={8.2}
          icon={IconShoppingCart}
          color="blue"
        />
        <KPICard
          title="المنتجات المباعة"
          value="342"
          change={-2.1}
          icon={IconPackage}
          color="orange"
        />
        <KPICard
          title="العملاء الجدد"
          value="23"
          change={15.3}
          icon={IconUsers}
          color="violet"
        />
      </SimpleGrid>

        <Grid gutter={{ base: 'sm', md: 'lg' }}>
          {/* Sales Chart */}
          <Grid.Col span={{ base: 12, md: 8 }}>
            <Card withBorder p={{ base: 'md', md: 'lg' }} radius="md" h={{ base: 300, md: 400 }}>
              <Group justify="space-between" mb="md" wrap="nowrap">
                <Text fw={600} size={{ base: 'md', md: 'lg' }}>المبيعات والأرباح الشهرية</Text>
                <Badge color="blue" variant="light" size={{ base: 'sm', md: 'md' }}>آخر 6 أشهر</Badge>
              </Group>
              <AreaChart
                h={{ base: 200, md: 300 }}
                data={salesData}
                dataKey="month"
                series={[
                  { name: 'sales', label: 'المبيعات', color: 'blue.6' },
                  { name: 'profit', label: 'الأرباح', color: 'green.6' },
                ]}
                curveType="linear"
                withLegend
                legendProps={{ verticalAlign: 'bottom' }}
              />
            </Card>
          </Grid.Col>

        {/* Branch Performance */}
        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder p="lg" radius="md" h={400}>
            <Text fw={600} size="lg" mb="md">أداء الفروع</Text>
            <Stack gap="md">
              <div>
                <Group justify="space-between" mb="xs">
                  <Text size="sm">الفرع الرئيسي</Text>
                  <Text size="sm" fw={500}>85%</Text>
                </Group>
                <Progress value={85} color="blue" size="sm" />
              </div>
              <div>
                <Group justify="space-between" mb="xs">
                  <Text size="sm">فرع جدة</Text>
                  <Text size="sm" fw={500}>72%</Text>
                </Group>
                <Progress value={72} color="green" size="sm" />
              </div>
              <div>
                <Group justify="space-between" mb="xs">
                  <Text size="sm">فرع الدمام</Text>
                  <Text size="sm" fw={500}>68%</Text>
                </Group>
                <Progress value={68} color="orange" size="sm" />
              </div>
              
              <Center mt="xl">
                <RingProgress
                  size={120}
                  thickness={12}
                  sections={[
                    { value: 85, color: 'blue', tooltip: 'الفرع الرئيسي - 85%' },
                    { value: 72, color: 'green', tooltip: 'فرع جدة - 72%' },
                    { value: 68, color: 'orange', tooltip: 'فرع الدمام - 68%' },
                  ]}
                  label={
                    <Text c="dimmed" fw={700} ta="center" size="xs">
                      متوسط الأداء
                      <br />
                      75%
                    </Text>
                  }
                />
              </Center>
            </Stack>
          </Card>
        </Grid.Col>

        {/* Inventory Alerts */}
        <Grid.Col span={12}>
          <Card withBorder p="lg" radius="md">
            <Group justify="space-between" mb="md">
              <Text fw={600} size="lg">تنبيهات المخزون</Text>
              <Badge color="red" variant="light">4 تنبيهات</Badge>
            </Group>
            <ScrollArea>
              <Table>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>المنتج</Table.Th>
                    <Table.Th>المخزون الحالي</Table.Th>
                    <Table.Th>الحد الأدنى</Table.Th>
                    <Table.Th>الحالة</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {inventoryAlerts.map((alert, index) => (
                    <Table.Tr key={index}>
                      <Table.Td>{alert.product}</Table.Td>
                      <Table.Td>{alert.currentStock}</Table.Td>
                      <Table.Td>{alert.minStock}</Table.Td>
                      <Table.Td>
                        <Badge
                          color={
                            alert.status === 'critical' ? 'red' :
                            alert.status === 'low' ? 'orange' : 'green'
                          }
                          variant="light"
                          leftSection={
                            alert.status === 'ok' ? 
                            <IconCheck size="0.8rem" /> : 
                            <IconAlertTriangle size="0.8rem" />
                          }
                        >
                          {alert.status === 'critical' ? 'حرج' :
                           alert.status === 'low' ? 'منخفض' : 'طبيعي'}
                        </Badge>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>
          </Card>
        </Grid.Col>
        </Grid>
      </Stack>
    </div>
  );
}
