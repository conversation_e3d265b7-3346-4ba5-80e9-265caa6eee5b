import React, { useState, useEffect } from 'react';
import {
  Paper,
  TextInput,
  PasswordInput,
  Button,
  Title,
  Text,
  Container,
  Group,
  Stack,
  Image,
  Center,
  Alert,
  SegmentedControl,
  Checkbox,
  Anchor,
  Divider,
  Loader,
  Badge,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import {
  IconAlertCircle,
  IconBuilding,
  IconUser,
  IconLock,
  IconMail,
  IconId,
  IconShield,
  IconUserCheck,
} from '@tabler/icons-react';
import { useAuthStore, UserType, LoginCredentials } from '../../stores/authStore';
import { useTranslation } from 'react-i18next';

export function LoginScreen() {
  const { t } = useTranslation();
  const {
    loginMode,
    setLoginMode,
    validateCompany,
    loginOwner,
    loginEmployee,
    company
  } = useAuthStore();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [companyValidated, setCompanyValidated] = useState(false);
  const [validatingCompany, setValidatingCompany] = useState(false);

  const form = useForm<LoginCredentials>({
    initialValues: {
      companyCode: '',
      userType: 'OWNER',
      email: '',
      employeeNumber: '',
      password: '',
      rememberMe: false,
    },
    validate: {
      companyCode: (value) => (!value ? t('auth.companyCodeRequired') : null),
      email: (value, values) => {
        if (values.userType === 'OWNER' && !value) {
          return t('auth.emailRequired');
        }
        if (values.userType === 'OWNER' && value && !/\S+@\S+\.\S+/.test(value)) {
          return t('auth.emailInvalid');
        }
        return null;
      },
      employeeNumber: (value, values) => {
        if (values.userType === 'EMPLOYEE' && !value) {
          return t('auth.employeeNumberRequired');
        }
        return null;
      },
      password: (value, values) => {
        if (!value) return t('auth.passwordRequired');

        // Different password requirements for owners vs employees
        if (values.userType === 'OWNER') {
          if (value.length < 12) return t('auth.ownerPasswordTooShort');
          if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/.test(value)) {
            return t('auth.ownerPasswordWeak');
          }
        } else {
          if (value.length < 8) return t('auth.employeePasswordTooShort');
          if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
            return t('auth.employeePasswordWeak');
          }
        }
        return null;
      },
    },
  });

  // Update form when login mode changes
  useEffect(() => {
    form.setFieldValue('userType', loginMode);
    setError(null);
  }, [loginMode]);

  // Validate company when company code changes
  useEffect(() => {
    const companyCode = form.values.companyCode;
    if (companyCode && companyCode.length >= 3) {
      const timeoutId = setTimeout(async () => {
        setValidatingCompany(true);
        const validCompany = await validateCompany(companyCode);
        setCompanyValidated(!!validCompany);
        setValidatingCompany(false);

        if (!validCompany) {
          setError(t('auth.companyNotFound'));
        } else {
          setError(null);
        }
      }, 500);

      return () => clearTimeout(timeoutId);
    } else {
      setCompanyValidated(false);
    }
  }, [form.values.companyCode, validateCompany, t]);

  const handleSubmit = async (values: LoginCredentials) => {
    if (!companyValidated) {
      setError(t('auth.validateCompanyFirst'));
      return;
    }

    console.log('Login form submitted:', values);
    setLoading(true);
    setError(null);

    try {
      let success = false;

      if (values.userType === 'OWNER') {
        console.log('Attempting owner login...');
        success = await loginOwner(values);
        if (!success) {
          console.log('Owner login failed');
          setError(t('auth.ownerLoginFailed'));
        } else {
          console.log('Owner login successful');
        }
      } else {
        console.log('Attempting employee login...');
        success = await loginEmployee(values);
        if (!success) {
          console.log('Employee login failed');
          setError(t('auth.employeeLoginFailed'));
        } else {
          console.log('Employee login successful');
        }
      }
    } catch (err) {
      console.error('Login error:', err);
      setError(t('auth.loginError'));
    } finally {
      setLoading(false);
    }
  };

  const handleModeChange = (value: string) => {
    const newMode = value as UserType;
    setLoginMode(newMode);
    form.setFieldValue('userType', newMode);

    // Clear user-specific fields when switching modes
    if (newMode === 'OWNER') {
      form.setFieldValue('employeeNumber', '');
    } else {
      form.setFieldValue('email', '');
    }
  };

  return (
    <Container
      size={{ base: 380, sm: 420, md: 480 }}
      px={{ base: 'sm', sm: 'md' }}
      style={{
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        overflow: 'auto'
      }}
    >
      <Paper
        withBorder
        shadow="md"
        p={{ base: 'md', sm: 'lg', md: 'xl' }}
        radius="md"
        style={{
          width: '100%',
          maxWidth: '100%',
          margin: 'auto'
        }}
      >
        <Center mb="xl">
          <Stack align="center" gap="xs">
            <Image
              src="/assets/logo.png"
              alt="Dimensions ERP"
              h={60}
              w="auto"
              fallbackSrc="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiByeD0iMTIiIGZpbGw9IiMyMjhiZTYiLz4KPHRleHQgeD0iMzAiIHk9IjM1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+RDwvdGV4dD4KPC9zdmc+"
            />
            <Title order={2} ta="center" c="blue">
              {t('app.title')}
            </Title>
            <Text c="dimmed" size="sm" ta="center">
              {t('app.subtitle')}
            </Text>
          </Stack>
        </Center>

        {/* Login Mode Selector */}
        <Center mb="lg">
          <SegmentedControl
            value={loginMode}
            onChange={handleModeChange}
            data={[
              {
                value: 'OWNER',
                label: (
                  <Group gap="xs">
                    <IconShield size="1rem" />
                    <Text size="sm">مالك الشركة</Text>
                  </Group>
                ),
              },
              {
                value: 'EMPLOYEE',
                label: (
                  <Group gap="xs">
                    <IconUserCheck size="1rem" />
                    <Text size="sm">موظف</Text>
                  </Group>
                ),
              },
            ]}
            size="md"
            radius="md"
          />
        </Center>

        {error && (
          <Alert icon={<IconAlertCircle size="1rem" />} color="red" mb="md">
            {error}
          </Alert>
        )}

        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack>
            {/* Company Code Field */}
            <TextInput
              label="رمز الشركة"
              placeholder="أدخل رمز الشركة"
              leftSection={<IconBuilding size="1rem" />}
              rightSection={
                validatingCompany ? (
                  <Loader size="xs" />
                ) : companyValidated ? (
                  <Badge color="green" size="xs">
                    تم التحقق
                  </Badge>
                ) : null
              }
              {...form.getInputProps('companyCode')}
              disabled={loading}
            />

            {/* Show company info when validated */}
            {companyValidated && company && (
              <Alert color="green" variant="light">
                <Group gap="xs">
                  <IconBuilding size="1rem" />
                  <Text size="sm" fw={500}>
                    {company.nameAr}
                  </Text>
                </Group>
              </Alert>
            )}

            <Divider />

            {/* Owner Login Fields */}
            {loginMode === 'OWNER' && (
              <>
                <TextInput
                  label="البريد الإلكتروني"
                  placeholder="أدخل البريد الإلكتروني"
                  leftSection={<IconMail size="1rem" />}
                  type="email"
                  {...form.getInputProps('email')}
                  disabled={loading || !companyValidated}
                />
              </>
            )}

            {/* Employee Login Fields */}
            {loginMode === 'EMPLOYEE' && (
              <>
                <TextInput
                  label="رقم الموظف"
                  placeholder="أدخل رقم الموظف"
                  leftSection={<IconId size="1rem" />}
                  {...form.getInputProps('employeeNumber')}
                  disabled={loading || !companyValidated}
                />
              </>
            )}

            {/* Password Field */}
            <PasswordInput
              label="كلمة المرور"
              placeholder={
                loginMode === 'OWNER'
                  ? "كلمة مرور قوية (12 حرف على الأقل)"
                  : "كلمة المرور (8 أحرف على الأقل)"
              }
              leftSection={<IconLock size="1rem" />}
              {...form.getInputProps('password')}
              disabled={loading || !companyValidated}
            />

            {/* Remember Me */}
            <Checkbox
              label="تذكرني"
              {...form.getInputProps('rememberMe', { type: 'checkbox' })}
              disabled={loading || !companyValidated}
            />

            <Button
              type="submit"
              fullWidth
              mt="xl"
              loading={loading}
              disabled={!companyValidated}
            >
              {loginMode === 'OWNER' ? 'دخول المالك' : 'دخول الموظف'}
            </Button>
          </Stack>
        </form>

        {/* Password Reset Link */}
        <Group justify="center" mt="md">
          <Text size="sm" c="dimmed">
            نسيت كلمة المرور؟{' '}
            <Anchor size="sm" c="blue">
              {loginMode === 'OWNER' ? 'إعادة تعيين عبر البريد' : 'اتصل بالإدارة'}
            </Anchor>
          </Text>
        </Group>

        {/* Employee Registration Link */}
        {loginMode === 'EMPLOYEE' && (
          <Group justify="center" mt="xs">
            <Text size="sm" c="dimmed">
              موظف جديد؟{' '}
              <Anchor size="sm" c="blue">
                طلب حساب جديد
              </Anchor>
            </Text>
          </Group>
        )}

        {/* Password Requirements */}
        {form.values.password && (
          <Alert color="blue" variant="light" mt="md">
            <Text size="xs" fw={500} mb="xs">
              متطلبات كلمة المرور:
            </Text>
            <Stack gap="xs">
              {loginMode === 'OWNER' ? (
                <>
                  <Text size="xs" c={form.values.password.length >= 12 ? 'green' : 'red'}>
                    • 12 حرف على الأقل
                  </Text>
                  <Text size="xs" c={/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])/.test(form.values.password) ? 'green' : 'red'}>
                    • أحرف كبيرة وصغيرة وأرقام ورموز خاصة
                  </Text>
                </>
              ) : (
                <>
                  <Text size="xs" c={form.values.password.length >= 8 ? 'green' : 'red'}>
                    • 8 أحرف على الأقل
                  </Text>
                  <Text size="xs" c={/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(form.values.password) ? 'green' : 'red'}>
                    • أحرف كبيرة وصغيرة وأرقام
                  </Text>
                </>
              )}
            </Stack>
          </Alert>
        )}
      </Paper>
    </Container>
  );
}
