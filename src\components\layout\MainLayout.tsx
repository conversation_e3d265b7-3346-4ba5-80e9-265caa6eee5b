import React, { useState, useEffect } from 'react';
import { App<PERSON><PERSON>, Burger, Group, Text, UnstyledButton, Avatar, Menu, rem, Badge, Alert } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconChevronDown, IconLogout, IconSettings, IconUser, IconClock, IconShield, IconUserCheck } from '@tabler/icons-react';
import { Sidebar } from './Sidebar';
import { useAuthStore } from '../../stores/authStore';
import { Dashboard } from '../modules/Dashboard';
import { ProductSearch } from '../modules/ProductSearch';
// TODO: Re-enable when services are ready
// import { PermissionManager } from '../../utils/permissions';
// import { sessionManager } from '../../services/sessionService';
// import { auditService } from '../../services/auditService';

export function MainLayout() {
  const [opened, { toggle }] = useDisclosure();
  const [activeModule, setActiveModule] = useState('dashboard');
  const [sessionWarning, setSessionWarning] = useState(false);
  const { user, company, branch, logout } = useAuthStore();

  // Session monitoring - temporarily disabled
  useEffect(() => {
    // TODO: Implement session monitoring when session management is ready
    console.log('User logged in:', user);
  }, [user, logout]);

  const handleModuleChange = (module: string) => {
    // TODO: Implement permission checking when user structure is ready
    setActiveModule(module);
  };

  const handleLogout = async () => {
    // TODO: Implement audit logging when ready
    logout();
  };

  const getUserDisplayName = () => {
    return user?.fullName || 'مستخدم';
  };

  const getUserRole = () => {
    return user?.role || 'مستخدم';
  };

  const renderModule = () => {
    switch (activeModule) {
      case 'dashboard':
        return <Dashboard />;
      case 'products':
        return <ProductSearch />;
      case 'inventory':
        return <div>المخزون</div>;
      case 'pos':
        return <div>نقطة البيع</div>;
      case 'sales':
        return <div>المبيعات</div>;
      case 'purchases':
        return <div>المشتريات</div>;
      case 'customers':
        return <div>العملاء</div>;
      case 'suppliers':
        return <div>الموردين</div>;
      case 'employees':
        return <div>الموظفين</div>;
      case 'accounting':
        return <div>المحاسبة</div>;
      case 'reports':
        return <div>التقارير</div>;
      case 'settings':
        return <div>الإعدادات</div>;
      default:
        return <Dashboard />;
    }
  };

  return (
    <AppShell
      header={{ height: 60 }}
      navbar={{
        width: 280,
        breakpoint: 'sm',
        collapsed: { mobile: !opened },
      }}
      padding="md"
    >
      <AppShell.Header>
        <Group h="100%" px="md" justify="space-between">
          <Group>
            <Burger opened={opened} onClick={toggle} hiddenFrom="sm" size="sm" />
            <Text size="lg" fw={600} c="blue">
              {company?.nameAr}
            </Text>
            <Text size="sm" c="dimmed">
              - {branch?.nameAr}
            </Text>
            {user?.role !== 'admin' && (
              <Badge color="blue" variant="light" size="sm">
                موظف
              </Badge>
            )}
          </Group>

          <Group gap="md">
            {/* Session warning - temporarily disabled */}

            <Menu shadow="md" width={250}>
              <Menu.Target>
                <UnstyledButton>
                  <Group gap="xs">
                    <Avatar size="sm" radius="xl" color={user?.role === 'admin' ? 'red' : 'blue'}>
                      {getUserDisplayName().charAt(0)}
                    </Avatar>
                    <div style={{ flex: 1 }}>
                      <Group gap="xs">
                        <Text size="sm" fw={500}>
                          {getUserDisplayName()}
                        </Text>
                        {user?.role === 'admin' ? (
                          <IconShield size="0.8rem" color="red" />
                        ) : (
                          <IconUserCheck size="0.8rem" color="blue" />
                        )}
                      </Group>
                      <Text c="dimmed" size="xs">
                        {getUserRole()}
                      </Text>
                    </div>
                    <IconChevronDown style={{ width: rem(14), height: rem(14) }} />
                  </Group>
                </UnstyledButton>
              </Menu.Target>

              <Menu.Dropdown>
                <Menu.Label>
                  {user?.role === 'admin' ? 'مدير النظام' : 'موظف'}
                </Menu.Label>

                <Menu.Item leftSection={<IconUser style={{ width: rem(14), height: rem(14) }} />}>
                  الملف الشخصي
                </Menu.Item>

                {user?.role === 'admin' && (
                  <Menu.Item leftSection={<IconSettings style={{ width: rem(14), height: rem(14) }} />}>
                    إعدادات الشركة
                  </Menu.Item>
                )}

                <Menu.Divider />

                <Menu.Item
                  color="red"
                  leftSection={<IconLogout style={{ width: rem(14), height: rem(14) }} />}
                  onClick={handleLogout}
                >
                  تسجيل الخروج
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
          </Group>
        </Group>
      </AppShell.Header>

      <AppShell.Navbar p="md">
        <Sidebar
          activeModule={activeModule}
          onModuleChange={handleModuleChange}
          userPermissions={user?.permissions || ['*']}
        />
      </AppShell.Navbar>

      <AppShell.Main>
        {renderModule()}
      </AppShell.Main>
    </AppShell>
  );
}
