import React, { useState, useEffect } from 'react';
import { App<PERSON><PERSON>, Burger, Group, Text, UnstyledButton, Avatar, Menu, rem, Badge, Alert } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconChevronDown, IconLogout, IconSettings, IconUser, IconClock, IconShield, IconUserCheck } from '@tabler/icons-react';
import { Sidebar } from './Sidebar';
import { useAuthStore } from '../../stores/authStore';
import { Dashboard } from '../modules/Dashboard';
import { ProductSearch } from '../modules/ProductSearch';
// TODO: Re-enable when services are ready
// import { PermissionManager } from '../../utils/permissions';
// import { sessionManager } from '../../services/sessionService';
// import { auditService } from '../../services/auditService';

export function MainLayout() {
  const [opened, { toggle }] = useDisclosure();
  const [activeModule, setActiveModule] = useState('dashboard');
  const [sessionWarning, setSessionWarning] = useState(false);
  const { user, company, branch, logout } = useAuthStore();

  // Session monitoring - temporarily disabled
  useEffect(() => {
    // TODO: Implement session monitoring when session management is ready
  }, [user, logout]);

  const handleModuleChange = (module: string) => {
    // TODO: Implement permission checking when user structure is ready
    setActiveModule(module);
  };

  const handleLogout = async () => {
    // TODO: Implement audit logging when ready
    logout();
  };

  const getUserDisplayName = () => {
    return user?.fullName || 'مستخدم';
  };

  const getUserRole = () => {
    return user?.role || 'مستخدم';
  };

  const renderModule = () => {
    switch (activeModule) {
      case 'dashboard':
        return <Dashboard />;
      case 'products':
        return <ProductSearch />;
      case 'inventory':
        return <div>المخزون</div>;
      case 'pos':
        return <div>نقطة البيع</div>;
      case 'sales':
        return <div>المبيعات</div>;
      case 'purchases':
        return <div>المشتريات</div>;
      case 'customers':
        return <div>العملاء</div>;
      case 'suppliers':
        return <div>الموردين</div>;
      case 'employees':
        return <div>الموظفين</div>;
      case 'accounting':
        return <div>المحاسبة</div>;
      case 'reports':
        return <div>التقارير</div>;
      case 'settings':
        return <div>الإعدادات</div>;
      default:
        return <Dashboard />;
    }
  };

  return (
    <AppShell
      header={{ height: { base: 60, md: 70 } }}
      navbar={{
        width: { base: 280, md: 300, lg: 320 },
        breakpoint: 'md',
        collapsed: { mobile: !opened, desktop: false },
      }}
      padding={{ base: 'xs', sm: 'sm', md: 'md', lg: 'lg' }}
      style={{ height: '100vh', overflow: 'hidden' }}
    >
      <AppShell.Header style={{ borderBottom: '1px solid var(--mantine-color-gray-3)' }}>
        <Group h="100%" px={{ base: 'sm', md: 'md' }} justify="space-between" wrap="nowrap">
          <Group gap={{ base: 'xs', md: 'sm' }} wrap="nowrap">
            <Burger opened={opened} onClick={toggle} hiddenFrom="md" size="sm" />
            <Group gap="xs" wrap="nowrap" visibleFrom="sm">
              <Text size={{ base: 'md', md: 'lg' }} fw={600} c="blue" truncate>
                {company?.nameAr}
              </Text>
              <Text size={{ base: 'xs', md: 'sm' }} c="dimmed" truncate>
                - {branch?.nameAr}
              </Text>
            </Group>
            {user?.role !== 'admin' && (
              <Badge color="blue" variant="light" size="sm" hiddenFrom="xs">
                موظف
              </Badge>
            )}
          </Group>

          <Group gap={{ base: 'xs', md: 'md' }} wrap="nowrap">
            {/* Session warning - temporarily disabled */}

            <Menu shadow="md" width={{ base: 200, md: 250 }} position="bottom-end">
              <Menu.Target>
                <UnstyledButton>
                  <Group gap="xs" wrap="nowrap">
                    <Avatar size={{ base: 'sm', md: 'md' }} radius="xl" color={user?.role === 'admin' ? 'red' : 'blue'}>
                      {getUserDisplayName().charAt(0)}
                    </Avatar>
                    <div style={{ flex: 1, minWidth: 0 }}>
                      <Group gap="xs" wrap="nowrap">
                        <Text size="sm" fw={500} truncate>
                          {getUserDisplayName()}
                        </Text>
                        {user?.role === 'admin' ? (
                          <IconShield size="0.8rem" color="red" />
                        ) : (
                          <IconUserCheck size="0.8rem" color="blue" />
                        )}
                      </Group>
                      <Text c="dimmed" size="xs" truncate>
                        {getUserRole()}
                      </Text>
                    </div>
                    <IconChevronDown style={{ width: rem(14), height: rem(14) }} />
                  </Group>
                </UnstyledButton>
              </Menu.Target>

              <Menu.Dropdown>
                <Menu.Label>
                  {user?.role === 'admin' ? 'مدير النظام' : 'موظف'}
                </Menu.Label>

                <Menu.Item leftSection={<IconUser style={{ width: rem(14), height: rem(14) }} />}>
                  الملف الشخصي
                </Menu.Item>

                {user?.role === 'admin' && (
                  <Menu.Item leftSection={<IconSettings style={{ width: rem(14), height: rem(14) }} />}>
                    إعدادات الشركة
                  </Menu.Item>
                )}

                <Menu.Divider />

                <Menu.Item
                  color="red"
                  leftSection={<IconLogout style={{ width: rem(14), height: rem(14) }} />}
                  onClick={handleLogout}
                >
                  تسجيل الخروج
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
          </Group>
        </Group>
      </AppShell.Header>

      <AppShell.Navbar
        p={{ base: 'sm', md: 'md' }}
        style={{
          borderRight: '1px solid var(--mantine-color-gray-3)',
          overflow: 'hidden'
        }}
      >
        <Sidebar
          activeModule={activeModule}
          onModuleChange={handleModuleChange}
          userPermissions={user?.permissions || ['*']}
        />
      </AppShell.Navbar>

      <AppShell.Main
        style={{
          height: 'calc(100vh - var(--app-shell-header-height))',
          overflow: 'auto',
          padding: 0
        }}
      >
        <div style={{
          padding: 'var(--mantine-spacing-md)',
          minHeight: '100%',
          display: 'flex',
          flexDirection: 'column'
        }}>
          {renderModule()}
        </div>
      </AppShell.Main>
    </AppShell>
  );
}
