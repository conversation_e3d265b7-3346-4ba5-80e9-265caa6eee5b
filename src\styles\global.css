@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

* {
  box-sizing: border-box;
}

html {
  direction: rtl;
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f9fa;
  height: 100%;
  overflow: hidden;
}

#root {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Custom scrollbar for RTL */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c2c5;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a6a7ab;
}

/* RTL specific adjustments */
.mantine-AppShell-navbar {
  border-left: 1px solid #e9ecef !important;
  border-right: none !important;
}

.mantine-NavLink-root {
  border-radius: 8px;
  margin: 2px 8px;
}

/* Arabic number formatting */
.arabic-numbers {
  font-feature-settings: 'lnum' 1;
}

/* Responsive utilities */
.scrollable-content {
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .mantine-AppShell-navbar {
    width: 100% !important;
  }

  .mantine-Grid-col {
    padding: 0.5rem !important;
  }

  .mantine-Card-root {
    padding: 1rem !important;
  }

  .mantine-Title-root {
    font-size: 1.25rem !important;
  }
}

/* Tablet responsive adjustments */
@media (max-width: 1024px) and (min-width: 769px) {
  .mantine-AppShell-navbar {
    width: 250px !important;
  }
}

/* Ensure proper scrolling on touch devices */
@media (hover: none) and (pointer: coarse) {
  .scrollable-content {
    -webkit-overflow-scrolling: touch;
  }
}

/* Print styles */
@media print {
  body {
    direction: rtl;
  }
  
  .no-print {
    display: none !important;
  }
}
