import React from 'react';
import { NavLink, Stack, Text, Divider, ScrollArea } from '@mantine/core';
import {
  IconDashboard,
  IconPackage,
  IconStack,
  IconCash,
  IconShoppingCart,
  IconTruck,
  IconUsers,
  IconBuilding,
  IconUsersGroup,
  IconCalculator,
  IconReportAnalytics,
  IconSettings,
  IconBrandWhatsapp,
  IconCreditCard,
  IconPercentage,
  IconFileInvoice,
  IconNetwork,
} from '@tabler/icons-react';

interface SidebarProps {
  activeModule: string;
  onModuleChange: (module: string) => void;
  userPermissions: string[];
}

const menuItems = [
  {
    id: 'dashboard',
    label: 'لوحة التحكم',
    icon: IconDashboard,
    color: 'blue',
  },
  {
    id: 'products',
    label: 'إدارة المنتجات',
    icon: IconPackage,
    color: 'green',
  },
  {
    id: 'inventory',
    label: 'إدارة المخزون',
    icon: IconStack,
    color: 'orange',
  },
  {
    id: 'pos',
    label: 'نقطة البيع',
    icon: IconCash,
    color: 'teal',
  },
  {
    id: 'sales',
    label: 'المبيعات',
    icon: IconShoppingCart,
    color: 'indigo',
  },
  {
    id: 'credit-sales',
    label: 'المبيعات الآجلة',
    icon: IconCreditCard,
    color: 'red',
  },
  {
    id: 'purchases',
    label: 'المشتريات',
    icon: IconTruck,
    color: 'violet',
  },
  {
    id: 'customers',
    label: 'العملاء',
    icon: IconUsers,
    color: 'cyan',
  },
  {
    id: 'suppliers',
    label: 'الموردين',
    icon: IconBuilding,
    color: 'grape',
  },
  {
    id: 'employees',
    label: 'الموظفين',
    icon: IconUsersGroup,
    color: 'pink',
  },
  {
    id: 'commissions',
    label: 'العمولات',
    icon: IconPercentage,
    color: 'yellow',
  },
  {
    id: 'marketing',
    label: 'التسويق',
    icon: IconBrandWhatsapp,
    color: 'lime',
  },
  {
    id: 'accounting',
    label: 'المحاسبة',
    icon: IconCalculator,
    color: 'dark',
  },
  {
    id: 'zatca',
    label: 'الفوترة الإلكترونية',
    icon: IconFileInvoice,
    color: 'blue',
  },
  {
    id: 'b2b',
    label: 'السوق التجاري',
    icon: IconNetwork,
    color: 'orange',
  },
  {
    id: 'reports',
    label: 'التقارير',
    icon: IconReportAnalytics,
    color: 'red',
  },
  {
    id: 'settings',
    label: 'الإعدادات',
    icon: IconSettings,
    color: 'gray',
  },
];

export function Sidebar({ activeModule, onModuleChange, userPermissions }: SidebarProps) {
  // Filter menu items based on user permissions
  const getAccessibleItems = (items: typeof menuItems) => {
    return items.filter(item => {
      // Owners have access to everything
      if (userPermissions.includes('*')) return true;

      // TODO: Implement permission checking when PermissionManager is ready
      // For now, allow access to all modules
      return true;
    });
  };

  const mainItems = getAccessibleItems(menuItems.slice(0, 4));
  const salesItems = getAccessibleItems(menuItems.slice(4, 8));
  const peopleItems = getAccessibleItems(menuItems.slice(8, 12));
  const accountingItems = getAccessibleItems(menuItems.slice(12));

  return (
    <ScrollArea
      style={{ height: 'calc(100vh - var(--app-shell-header-height) - 2rem)' }}
      scrollbarSize={6}
      scrollHideDelay={1000}
    >
      <Stack gap="xs" pb="sm">
        {mainItems.length > 0 && (
          <>
            <Text size="xs" tt="uppercase" fw={700} c="dimmed" mb="xs">
              القوائم الرئيسية
            </Text>

            {mainItems.map((item) => (
              <NavLink
                key={item.id}
                active={activeModule === item.id}
                label={item.label}
                leftSection={<item.icon size="1rem" />}
                onClick={() => onModuleChange(item.id)}
                color={item.color}
                style={{
                  borderRadius: 'var(--mantine-radius-sm)',
                  fontSize: '0.875rem',
                  padding: '0.5rem 0.75rem',
                  minHeight: 'auto',
                  height: 'auto'
                }}
              />
            ))}

            <Divider my="xs" />
          </>
        )}

        {salesItems.length > 0 && (
          <>
            <Text size="xs" tt="uppercase" fw={700} c="dimmed" mb="xs">
              المبيعات والمشتريات
            </Text>

            {salesItems.map((item) => (
              <NavLink
                key={item.id}
                active={activeModule === item.id}
                label={item.label}
                leftSection={<item.icon size="1rem" />}
                onClick={() => onModuleChange(item.id)}
                color={item.color}
                style={{
                  borderRadius: 'var(--mantine-radius-sm)',
                  fontSize: '0.875rem',
                  padding: '0.5rem 0.75rem',
                  minHeight: 'auto',
                  height: 'auto'
                }}
              />
            ))}

            <Divider my="xs" />
          </>
        )}

        {peopleItems.length > 0 && (
          <>
            <Text size="xs" tt="uppercase" fw={700} c="dimmed" mb="xs">
              إدارة الأشخاص
            </Text>

            {peopleItems.map((item) => (
              <NavLink
                key={item.id}
                active={activeModule === item.id}
                label={item.label}
                leftSection={<item.icon size="1rem" />}
                onClick={() => onModuleChange(item.id)}
                color={item.color}
                style={{
                  borderRadius: 'var(--mantine-radius-sm)',
                  fontSize: '0.875rem',
                  padding: '0.5rem 0.75rem',
                  minHeight: 'auto',
                  height: 'auto'
                }}
              />
            ))}

            <Divider my="xs" />
          </>
        )}

        {accountingItems.length > 0 && (
          <>
            <Text size="xs" tt="uppercase" fw={700} c="dimmed" mb="xs">
              المحاسبة والتقارير
            </Text>

            {accountingItems.map((item) => (
              <NavLink
                key={item.id}
                active={activeModule === item.id}
                label={item.label}
                leftSection={<item.icon size="1rem" />}
                onClick={() => onModuleChange(item.id)}
                color={item.color}
                style={{
                  borderRadius: 'var(--mantine-radius-sm)',
                  fontSize: '0.875rem',
                  padding: '0.5rem 0.75rem',
                  minHeight: 'auto',
                  height: 'auto'
                }}
              />
            ))}
          </>
        )}

        {/* Show message if user has no access to any modules */}
        {mainItems.length === 0 && salesItems.length === 0 && peopleItems.length === 0 && accountingItems.length === 0 && (
          <Text size="sm" c="dimmed" ta="center" mt="xl">
            لا توجد صلاحيات للوصول إلى أي وحدة
          </Text>
        )}
      </Stack>
    </ScrollArea>
  );
}
